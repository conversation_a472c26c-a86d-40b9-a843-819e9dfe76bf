{"name": "@baidu/bce-httpclient", "version": "1.0.0-rc.18", "description": "", "main": "dist/httpclient.js", "files": ["/dist", "/src"], "scripts": {"dev": "NODE_OPTIONS=--openssl-legacy-provider webpack-dev-server --config scripts/webpack.config.dev.js", "build": "NODE_OPTIONS=--openssl-legacy-provider webpack --config scripts/webpack.config.prod.js", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/baiducloud/fe-httpclient"}, "author": "yangwei14", "license": "MIT", "dependencies": {"@baiducloud/i18n": "^1.0.0-rc.23", "caniuse-lite": "^1.0.30001583", "jquery": "^3.7.1", "qrcode": "^1.5.3", "san": "^3.7.9"}, "devDependencies": {"@babel/core": "^7.5.4", "@babel/plugin-proposal-class-properties": "^7.5.0", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-runtime": "^7.5.0", "@babel/preset-env": "^7.5.4", "@babel/preset-typescript": "^7.7.4", "@babel/runtime": "^7.5.4", "@baiducloud/restclient": "1.0.5-beta", "@types/jquery": "^3.5.29", "@types/qrcode": "^1.5.5", "autoprefixer": "^9.6.1", "babel-eslint": "^10.0.2", "babel-loader": "^8.0.5", "css-loader": "^2.1.1", "cssnano": "^4.1.10", "eslint": "^5.15.3", "eslint-config-airbnb-base": "^13.2.0", "eslint-formatter-pretty": "^2.1.0", "eslint-plugin-import": "^2.18.0", "events": "^3.0.0", "html-webpack-plugin": "^4.0.0-beta.3", "less": "^2.7.3", "less-loader": "^4.1.0", "mini-css-extract-plugin": "^0.5.0", "postcss-loader": "^3.0.0", "script-loader": "^0.7.2", "style-loader": "^0.23.1", "ts-loader": "^5.3.3", "typescript": "^3.5.3", "uglifyjs-webpack-plugin": "^2.1.2", "url-loader": "^1.1.2", "webpack": "^4.35.3", "webpack-cli": "^3.3.5", "webpack-dev-server": "^3.7.2", "webpack-merge": "^4.2.1"}}