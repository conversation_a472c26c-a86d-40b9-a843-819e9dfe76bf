/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.config.dev.js
 * <AUTHOR>
 */

const merge = require('webpack-merge');

const baseConfig = require('./webpack.config.base');

const port = process.env.PORT || 8222;
const publicPath = `http://localhost:${port}/`;


exports = module.exports = merge.smart(baseConfig, {
    mode: 'development',

    entry: {
        'httpclient': [
            `webpack-dev-server/client?${publicPath}`,
            'webpack/hot/only-dev-server',
            './src/index.ts'
        ]
    },

    output: {
        publicPath
    },

    devServer: {
        port,
        publicPath,
        compress: true,
        noInfo: true,
        stats: 'errors-only',
        inline: true,
        lazy: false,
        hot: true,
        // open: true,
        disableHostCheck: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true
        },
        watchOptions: {
            aggregateTimeout: 300,
            ignored: /node_modules/,
            poll: 100
        },
        historyApiFallback: {
            verbose: true,
            disableDotRule: false
        }
    }
});
