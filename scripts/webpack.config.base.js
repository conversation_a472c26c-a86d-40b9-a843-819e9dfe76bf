/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.base.config.js
 * <AUTHOR>
 */

const path = require('path');
const webpack = require('webpack');
const cssnano = require('cssnano');
const autoprefixer = require('autoprefixer');

exports = module.exports = {
    mode: 'none',

    context: path.resolve(__dirname, '../'),

    entry: {
        'httpclient': './src/index.ts'
    },

    output: {
        path: path.join(process.cwd(), 'dist'),
        filename: '[name].js',
        libraryTarget: 'umd'
    },

    resolve: {
        extensions: ['.js', '.ts', '.json']
    },

    module: {
        rules: [
            {
                test: /\.(ts|js)$/,
                exclude: /node_modules/,
                use: 'babel-loader'
            },
            {
                test: /\.less$/,
                use: [
                    {loader: 'style-loader'},
                    {loader: 'css-loader'},
                    {
                        loader: 'postcss-loader',
                        options: {
                            plugins: [
                                cssnano({preset: 'default'}),
                                autoprefixer()
                            ]
                        }
                    },
                    {loader: 'less-loader'}
                ]
            },
            // SVG Font
            {
                test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
                use: {
                    loader: 'url-loader',
                    options: {
                        limit: 10000,
                        mimetype: 'image/svg+xml'
                    }
                }
            },
            // Common Image Formats
            {
                test: /\.(?:ico|gif|png|jpg|jpeg|webp)$/,
                use: 'url-loader'
            }
        ]
    },

    plugins: [
        new webpack.EnvironmentPlugin({
            VERSION: JSON.stringify(process.env.VERSION || 'DEV'),
            NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'production')
        }),

        new webpack.NamedModulesPlugin(),

        new webpack.SourceMapDevToolPlugin({
            filename: '[name]-[hash:8].js.map'
        })
    ],

    externals: [
        'san',
        'lodash',
        'moment',
        'bce-ui/san',
        '@baiducloud/bce-ui/san',
        '@baiducloud/i18n'
    ]
};
