/**
 * @file Notification.ts (迁移并修改自SUI的notification)
 * <AUTHOR>
 */

import { Component } from 'san';

// 避免与sui的样式覆盖
const prefixCls = 'httpclient-notification';

const tpl = String.raw`
    <div class="${prefixCls}-base" s-transition="transition">
        <div class="{{baseClass}}" style="{{baseStyle}}">
            <div class="${prefixCls}-wrapper">
                <div class="${prefixCls}-icon s-icon-{{icon}}" s-if="icon">
                    <i class="${prefixCls}-icon-error" on-click="copyRequestId"/>
                </div>
                <div class="${prefixCls}-content">
                    <h3 s-if="title" title="{{title}}">{{title}}</h3>
                    <div class="content-wrapper">
                        <p
                            s-if="content"
                            class="{{title
                            ? 'content' : ''}}"
                        >
                            {{content}}
                        </p>
                        <s-custom-content s-else />
                    </div>
                </div>
                <div class="${prefixCls}-close">
                    <span class="icon-wrapper">
                        <span s-if="{{duration >= 0}}">({{duration}}s)</span>
                        <i class="${prefixCls}-icon-close" on-click="close"/>
                    </span>
                </div>
            </div>
        </div>
    </div>
`;

export default class Notification extends Component {
    _timer: any = null;

    static template = tpl;

    static components = {}

    static computed: any = {
        baseClass() {
            const placement = this.data.get('placement');
            return `${prefixCls}-${placement} ${prefixCls}`;
        },
        baseStyle() {
            const style: { width?: string } = {};
            const width: number = this.data.get('width');
            if (width != null) {
                style.width = `${width}px`;
            }
            return style;
        }
    };

    transition = {
        leave(el: any, done: Function) {
            if (el) {
                const noti = el.getElementsByTagName('div')[0];
                noti.classList.add(`${prefixCls}-out`);
                setTimeout(done, 250);
            }
        }
    };

    initData() {
        return {
            content: '',
            title: '',
            icon: '',
            showConfirmButton: false,
            placement: '',
            top: 24,
            bottom: 24,
            duration: 5
        };
    }

    close(e: Event) {
        this.dispose && this.dispose();
    }

    onConfirm(e: Event) {
        this.dispose();
    }

    attached() {
        if (this.data.get('icon')) {
            console.warn('Notification组件icon属性即将废弃，请使用SanComponent传入图标');
        }
        if (this.data.get('duration') > 0) {
            this._timer = setInterval(() => {
                const duration = this.data.get('duration') || 0;

                if (typeof(duration) === 'number' || duration < 0) {
                    return;
                }

                if (duration - 1 > 0) {
                    this.data.set('duration', duration - 1);
                } else {
                    this.nextTick(() => this.dispose());
                }
            }, 1e3);
        }
    }

    disposed() {
        clearInterval(this._timer);
    }

    inited() {
        this._timer = null;
    }
}
