@prefix: httpclient;

// 动画
@keyframes notificationright {
    0% {
        right: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        right: 0;
    }
}
@-moz-keyframes notificationright {
    0% {
        right: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        right: 0;
    }
}
@-webkit-keyframes notificationright {
    0% {
        right: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        right: 0;
    }
}
@-o-keyframes notificationright {
    0% {
        right: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        right: 0;
    }
}
// left
@keyframes notificationleft {
    0% {
        left: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        left: 0;
    }
}
@-moz-keyframes notificationleft {
    0% {
        left: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        left: 0;
    }
}
@-webkit-keyframes notificationleft {
    0% {
        left: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        left: 0;
    }
}
@-o-keyframes notificationleft {
    0% {
        left: -50px;
        opacity: 0;
    }
    100% {
        opacity: 1;
        left: 0;
    }
}

@keyframes notificationout {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
        max-height: 0;
        height: 0;
        border: 0;
    }
}

.@{prefix}-notification-container {
    border-radius: 4px;
    position: fixed;
    z-index: 999999;
    .@{prefix}-notification-base {
        clear: both;
    }
}

.@{prefix}-notification-container-topRight,
.@{prefix}-notification-container-bottomRight {
    .@{prefix}-notification-base {
        float: right;
    }
}

.@{prefix}-notification-container-topLeft,
.@{prefix}-notification-container-bottomLeft {
    .@{prefix}-notification-base {
        float: left;
    }
}

.@{prefix}-notification-out {
    animation: notificationout .3s ease-in-out !important;
    -moz-animation: notificationout .3s ease-in-out !important;
    -webkit-animation: notificationout .3s ease-in-out !important;
    -o-animation: notificationout .3s ease-in-out !important;
}

.@{prefix}-notification {
    border-radius: 4px;
    text-align: left;
    display: block;
    overflow: hidden;
    position: relative;
    margin-bottom: 8px;
    padding: 16px;
    background-color: #FFFFFF;
    box-shadow: 0 2px 8px 0 fade(#070C14, 12%);
    border: 1px solid #E8E9EB;
    width: 280px;

    &.@{prefix}-notification-topRight,
    &.@{prefix}-notification-bottomRight {
        float: right;
        animation: notificationright .3s ease-in-out;
        -moz-animation: notificationright .3s ease-in-out;
        -webkit-animation: notificationright .3s ease-in-out;
        -o-animation: notificationright .3s ease-in-out;
    }
    &.@{prefix}-notification-topLeft,
    &.@{prefix}-notification-bottomLeft {
        float: left;
        animation: notificationleft .3s ease-in-out;
        -moz-animation: notificationleft .3s ease-in-out;
        -webkit-animation: notificationleft .3s ease-in-out;
        -o-animation: notificationleft .3s ease-in-out;
    }

    .@{prefix}-notification-wrapper {
        display: flex;
        .@{prefix}-notification-icon {
            width: 25px;
            margin-top: 2px;
            .@{prefix}-icon {
                vertical-align: middle;
            }
            .iconfont {
                vertical-align: middle;
                font-size: 12px;
            }
        }

        .@{prefix}-notification-icon-error {
            position: relative;
            display: block;
            &:before {
                content: '';
                position: absolute;
                top: 0;
                background-color: #F33E3E;
                background-repeat: no-repeat;
                width: 16px;
                height: 16px;
                mask-image: url('../../assets/error.svg');
            }
        }

        .@{prefix}-notification-content {
            flex: 1;
            min-width: 0;
            margin-right: 8px;
            > h3 {
                font-size: 12px;
                font-weight: 500;
                color: #151B26;
                margin: 0;
                padding-right: 16px;
                height: 18px;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
            }
            .content-wrapper {
                > p {
                    margin: 0;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                    word-break: break-all;
                    color: #151B26;
                }
                .content {
                    line-height: 20px;
                }
            }
        }
        .@{prefix}-notification-close {
            height: 20px;
            width: 20px;
            display: inline-block;
            vertical-align: middle;
            > span.icon-wrapper {
                font-size: 12px;
                float: right;
                height: 18px;
                line-height: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                > span {
                    color: #B8BABF;
                }
                .@{prefix}-notification-icon-close {
                    fill: #84868C;
                    position: relative;
                    margin-left: 4px;
                    cursor: pointer;
                    &:before {
                        content: '';
                        position: absolute;
                        background-color: #84868C;
                        background-repeat: no-repeat;
                        top: -8px;
                        right: 1px;
                        width: 16px;
                        height: 16px;
                        mask-image: url('../../assets/close.svg');
                    }
                }
                .s-icon-button-able:hover {
                    fill: #2468F2 !important;
                }
                .s-icon-button-able:active {
                    fill: #303540 !important;
                }
            }
        }
    }
    .@{prefix}-notification-confirm {
        margin-top: 8px;
        text-align: right;
    }
}
.@{prefix}-notification-with-title {
    padding: 16px;
    .@{prefix}-notification-wrapper {
        .@{prefix}-notification-icon {
            line-height: 22px;
            height: 22px;
        }
        .@{prefix}-notification-content {
            > h3 {
                line-height: 22px;
                height: 22px;
            }
        }
    }
}
