/**
 * @file Container.ts 迁移并修改自SUI的notification
 * <AUTHOR>
 */
import { Component } from 'san';
import { PlacementType } from './main';

const prefixCls = 'httpclient-notification-container';

export default class NotificationContainer extends Component {
    static template = String.raw`
        <div class="{{baseClass}}" style="{{mainStyle}}">
        </div>
    `;

    static computed: any = {
        baseClass() {
            const placement = this.data.get('placement');
            return `${prefixCls}-${placement} ${prefixCls}`;
        },
        mainStyle() {
            const placement = this.data.get('placement');
            const top = this.data.get('top') || 24;
            const bottom = this.data.get('bottom') || 24;
            const left = 24;
            const right = 24;
            switch (placement) {

                case PlacementType.TOP_LEFT:
                    return `top: ${top}px; left: ${left}px`;

                case PlacementType.TOP_RIGHT:
                    return `top: ${top}px; right: ${right}px`;

                case PlacementType.BOTTOM_LEFT:
                    return `bottom: ${bottom}px; left: ${left}px`;

                case PlacementType.BOTTOM_RIGHT:
                    return `bottom: ${bottom}px; right: ${right}px`;

                default:
                    return `top: ${top}px; right: ${right}px`;
            }
        }
    };
}
