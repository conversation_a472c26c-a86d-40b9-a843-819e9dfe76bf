/**
 * @file main.ts Notification 入口 (迁移并修改自SUI的notification)
 * <AUTHOR>
 */

import { SanComponent } from 'san';

import { NotificationProps } from './type';

import Notification from './Notification';
import Container from './Container';

import './index.less';

interface KContainerProps {
    [K: string]: any;
}

enum TYPE {
    SUCCESS = 'success',
    INFO = 'info',
    WARNING = 'warning',
    ERROR = 'error'
}

export enum PlacementType {
    TOP_LEFT = 'topLeft',
    TOP_RIGHT = 'topRight',
    BOTTOM_LEFT = 'bottomLeft',
    BOTTOM_RIGHT = 'bottomRight'
}

const ICON: any = {
    success: 'ok',
    info: 'info',
    warning: 'warning',
    error: 'fail'
};

const KContainer: KContainerProps = {
    topLeft: null,
    topRight: null,
    bottomLeft: null,
    bottomRight: null
};

let DefaultOptions: NotificationProps = {
    title: '',
    showConfirmButton: false,
    duration: 5,
    placement: PlacementType.TOP_RIGHT
};
const existPlacement: string[] = [];
let invokeInstance: SanComponent<{}>[] = [];

const asPromise = (dialog: SanComponent<{}>) => {
    return new Promise((resolve, reject) => {
        dialog.on('confirm', () => {
            resolve();
            dialog.dispose();
        });
        dialog.on('close', () => {
            reject();
            dialog.dispose();
        });
    });
};

const kMaxQueueLen = 4;

export default class NotificationApi {
    // 限制数量
    static _invokeQueue(compInstance: Notification) {
        // 已经释放的忽略掉
        invokeInstance = invokeInstance.filter(r => !r.lifeCycle.is('disposed'));
        if (invokeInstance.length >= kMaxQueueLen) {
            invokeInstance.shift().dispose();
        }
        invokeInstance.push(compInstance);
    }

    // 初始化父组件
    static invokeContainer(
        placement: string | undefined = PlacementType.TOP_RIGHT,
        top: number | undefined,
        bottom: number | undefined
    ) {
        const container = new Container({
            data: { placement, top, bottom }
        });
        existPlacement.indexOf(placement) === -1 && container.attach(document.body);
        existPlacement.push(placement);
        KContainer[placement] = container;
        return KContainer[placement];
    }

    static from(type: any, content: string | SanComponent, config: NotificationProps) {
        // 默认右上角弹出
        // 避免人为设置 placement 为 undefined
        const placement = config.placement || DefaultOptions.placement || PlacementType.TOP_RIGHT;
        const { top, bottom } = config;

        const container: any = KContainer[placement]
            ? KContainer[placement]
            : this.invokeContainer(placement, top, bottom);
        const icon: string = config.icon ? config.icon : type ? ICON[type] : '';
        let instance = null;

        if (typeof content === 'function' && content.prototype.nodeType === 5) {
            
            Notification.components['s-custom-content'] = content;
            instance = new Notification({
                data: {
                    ...DefaultOptions,
                    ...config,
                    type,
                    icon
                }
            });
            this._invokeQueue(instance);
        } else if (typeof(content) === 'string') {
            instance = new Notification({
                data: {
                    ...DefaultOptions,
                    ...config,
                    type,
                    content,
                    icon
                }
            });
            this._invokeQueue(instance);
        } else {
            throw new TypeError('notification.from args expect `String` | `SanComponent`');
        }

        instance.attach(container.el);

        return asPromise(instance);
    }

    static config(options: NotificationProps) {
        DefaultOptions = { ...DefaultOptions, ...options };
    }

    static success(content: string | SanComponent, options: NotificationProps = {}) {
        return this.from(TYPE.SUCCESS, content, options);
    }

    static info(content: string | SanComponent, options: NotificationProps = {}) {
        return this.from(TYPE.INFO, content, options);
    }

    static warning(content: string | SanComponent, options: NotificationProps = {}) {
        return this.from(TYPE.WARNING, content, options);
    }

    static error(content: string | SanComponent, options: NotificationProps = {}) {
        return this.from(TYPE.ERROR, content, options);
    }

    static open(content: string | SanComponent, options: NotificationProps = {}) {
        // 默认没有icon
        const config = {
            ...options,
            duration: options.duration || -1
        };
        return this.from('', content, config);
    }

    static destroy() {
        invokeInstance.forEach(instance => {
            instance.dispose && instance.dispose();
        });
        invokeInstance = [];
    }
}
