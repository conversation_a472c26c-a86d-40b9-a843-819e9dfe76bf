/**
 * @file notification 组件的类型 (迁移并修改自SUI的notification)
 * <AUTHOR>
 */
export declare interface NotificationProps {
    /**
     * notification content
     * @default ''
     * @type string'
     */
    content?: string;

    /**
     * notification title
     * @default ''
     * @type string
     */
    title?: string;

    /**
     * close the notification after time, the unit is 'ms'
     * @default 5000
     * @type number
     */
    duration?: number;

    /**
     * show confirm button
     * @default false
     * @type boolean
     */
    showConfirmButton?: boolean;

    /**
     * the positon of notification
     * @default 'topLeft'
     * @type placementType
     */
    placement?: placementType;

    /**
     * the distance of notification to top, only placement is topLeft or topRight
     * @default 24
     * @type number
     */
    top?: number;

    /**
     * the distance of notification to bottom, only placement is bottomLeft or bottomRight
     * @default 24
     * @type 24
     */
    bottom?: number;
}
