/**
 * @file SMSCodeBox.js
 * <AUTHOR>
 */

import {Component} from 'san';

const tpl = `
    <div class="smscodebox">
        <s-input
            on-input="onInput"
            placeholder="{{placeholder}}"
            width="{{width}}"
            value="{=value=}"
            disabled="{{disabled}}" />
        <s-button on-click="onBtnClick"
            width="{{60}}"
            disabled="{{freezed || disabled}}">{{btnText}}</s-button>
    </div>
`;

export default function MFACheck(sui: any) {
    const {Button, Input} = sui;
    return class MFACheck extends Component {
        static template = tpl;

        static components = {
            's-input': Input,
            's-button': Button
        };

        initData() {
            return {
                freezed: false,
                disabled: false,
                freezeTime: 60,
                btnText: '发送验证码',
                value: '',
                stopCount: false,
                width: null,
                placeholder: '请输入验证码'
            };
        }

        onInput() {
            this.fire('input', '');
        }

        onBtnClick() {
            this.fire('click', '');
            this.data.set('freezed', true);
            let freezeTime = this.data.get('freezeTime');
            const countdown = () => {
                //  如果需要重试，则取消计时
                if (this.data.get('stopCount')) {
                    this.data.set('stopCount', false);
                    freezeTime = 0;
                }

                if (freezeTime <= 0) {
                    this.data.set('freezed', false);
                    this.data.set('btnText', '发送验证码');
                } else {
                    this.data.set('btnText', `剩余 ${freezeTime--} 秒`);
                    setTimeout(countdown, 1000);
                }
            };
            countdown();
        }
    };
}
