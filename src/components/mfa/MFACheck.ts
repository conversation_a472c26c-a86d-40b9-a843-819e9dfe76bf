/**
 * @file components/MFACheck.js
 * <AUTHOR>
 */

import $ from 'jquery';
import {Component} from 'san';
import QRCode from 'qrcode';
import SMSCodeBox from './SMSCodeBox';
import './index.less';

export default function MFACheck(sui: any) {
    const {Dialog, Button, Input, Select} = sui;
    return class extends Component {
        static template = `
            <template>
                <s-dialog open="{=open=}" title="安全验证" class="mfacheck" width="{{500}}" on-close="onCloseDialog">
                    <div class="mfacheck-header">
                        为了保护您的账户安全，请通过安全验证：
                        <template s-if="isCreateTotp"><br>请使用Google身份验证器或其他TOTP APP扫描二维码并绑定：</template>
                    </div>
                    <div class="mfacheck-body">
                        <div class="mfacheck-row" s-if="mfaType && !isCreateTotp">
                            <label>验证方式：</label>
                            <s-select
                                datasource="{{selectMfaOptions}}"
                                value="{{authType}}"
                                width="{{210}}"
                                height="{{30}}"
                                on-change="onChangeMfaType"
                            />
                        </div>
                        <!--短信验证-->
                        <template s-if="isPhone">
                            <div s-if="showPhoneNumber" class="mfacheck-row">
                                <label>您绑定的手机：</label>
                                {{mobile}}
                                <a s-if="changePhone" href="{{iamLink}}"
                                    class="mfacheck-change-link" target="_blank">更换手机</a>
                            </div>
                            <div class="mfacheck-row">
                                <label>短信验证码：</label>
                                <ui-smscodebox on-click="onSendCode" btnText="发送验证码" stopCount="{=stopCount=}"
                                    value="{=code=}" width="{{122}}" on-input="onCodeInput" />
                            </div>
                            <div class="mfacheck-row">
                                <label />
                                <span class="{{msgClass}}">{{message.text}}</span>
                            </div>
                        </template>
                        <!--软件OTP验证-->
                        <template s-else-if="mfaType">
                            <div style="text-align:center" s-if="isCreateTotp">
                                <canvas id="mfa-qrcode"></canvas>
                            </div>
                            <div class="mfacheck-row">
                                <label>{{isCreateTotp? '验证码1：': '验证码：'}}</label>
                                <s-input
                                    value="{=totpCode1=}"
                                    on-input="onCodeInput"
                                    width="{{208}}"
                                    placeholder="请输入6位验证码"
                                />
                            </div>
                            <div class="mfacheck-row" s-if="isCreateTotp">
                                <label>验证码2：</label>
                                <s-input
                                    value="{=totpCode2=}"
                                    on-input="onCodeInput"
                                    width="{{208}}"
                                    placeholder="30s后，请输入第二组6位验证码"
                                />
                            </div>
                            <div class="mfacheck-row" s-if="message.text">
                                <label />
                                <span class="{{msgClass}}">{{message.text}}</span>
                            </div>
                            <div class="mfacheck-switch-text" s-if="isMoreMFAType && isCreateTotp">
                                <a href="javascript:void(0);" on-click="onChangeMfaType({value:'phone'})">&lt;选择其他验证方式</a>
                            </div>
                        </template>
                    </div>
    
                    <div slot="footer">
                        <s-button on-click="onConfirmDialog" disabled="{{hasBtnConfirm}}" skin="primary">确定</s-button>
                        <s-button on-click="onCloseDialog">取消</s-button>
                    </div>
                </s-dialog>
            </template>
        `;

        components = {
            's-button': Button,
            's-input': Input,
            's-select': Select,
            's-dialog': Dialog,
            'ui-smscodebox': SMSCodeBox(sui)
        };

        initData() {
            return {
                open: true,
                code: '',
                mobile: '',
                stopCount: false,
                message: {},
                iamLink: `/iam/${location.search}#/iam/authinfo`,
                selectMfaOptions: [], // 验证方式下拉option
                authType: 'phone', // 验证方式下拉值
                isDevicesTotp: false, // 默认未绑定
                totpCode1: '', // 验证码1
                totpCode2: '', // 验证码2
                changePhone: true,
                showPhoneNumber: true,
                /** 确定按钮是否锁定，防止重复点击 */
                confirmBtnLocked: false
            };
        }

        static computed: {[x: string]: (this: {data: any}) => any} = {
            // 错误提示类名
            msgClass() {
                const level = this.data.get('message.level');
                return level ? 'mfacheck-message-success' : 'mfacheck-message-error';
            },
            // 是否有两种以上验证方式
            isMoreMFAType() {
                return this.data.get('selectMfaOptions').length >= 2;
            },
            // 非短信验证
            isPhone() {
                return this.data.get('authType') === 'phone';
            },
            // 是虚拟MFA并没有绑定
            isCreateTotp() {
                return this.data.get('authType') === 'totp' && !this.data.get('isDevicesTotp');
            },
            // 是否禁用确认按钮
            hasBtnConfirm() {
                const authType = this.data.get('authType');
                if (authType === 'phone') {
                    return !this.data.get('code');
                } else if (authType === 'totp') {
                    const isCreateTotp = this.data.get('isCreateTotp');
                    if (isCreateTotp) {
                        return !(this.data.get('totpCode1') && this.data.get('totpCode2'));
                    }
                    return !this.data.get('totpCode1');
                } else {
                    return false;
                }
            }
        };

        created() {
            const totp = this.data.get('totp');
            const mobile = this.data.get('mobile');
            // 兼容老方式的 MFA
            if (totp === undefined) {
                return;
            }
            // mfaType 为支持的校验类型
            let mfaType: string[] = [];
            mobile && mfaType.push('phone');
            // console-hub拦截返回totp: boolean
            // IAM 接口返回totp: string。。
            Boolean(totp) && mfaType.push('totp');
            this.data.set('mfaType', mfaType);

            const options = mfaType.map(item => ({
                text: item === 'phone' ? '短信验证码' : '软件动态码',
                value: item
            }));
            this.data.set('selectMfaOptions', options);
            const selectValue = options.length <= 1 ? options[0].value : 'phone';
            selectValue === 'totp' ? this.inspectTotpStatus(selectValue) : this.data.set('authType', selectValue);
        }

        inspectTotpStatus(value: string) {
            this.sendRequest({
                url: '/api/iam/user/mfa/devices/status',
                payload: {
                    type: value
                }
            })
                .then(({result}: any) => {
                    !result.status && this.createTotp(value);
                    this.data.set('isDevicesTotp', result.status);
                })
                .catch(e => {
                    this.data.merge('message', {
                        level: 0,
                        text: '软件动态码状态查询失败，请重试'
                    });
                })
                .finally(() => {
                    this.data.set('authType', value);
                });
        }

        createTotp(value: string) {
            this.sendRequest({
                url: '/api/iam/user/mfa/devices/create',
                payload: {
                    type: value
                }
            })
                .then(({result}: any) => {
                    QRCode.toCanvas(document.getElementById('mfa-qrcode'), result.secretSeedUrl, {
                        width: 116,
                        color: {
                            dark: '#000000',
                            light: '#ffffff'
                        }
                    });
                })
                .catch(e => {
                    this.data.merge('message', {
                        level: 0,
                        text: '软件动态码二维码获取失败，请重试'
                    });
                });
        }

        onChangeMfaType({value}: any) {
            value === 'totp' ? this.inspectTotpStatus(value) : this.data.set('authType', value);
            this.onCodeInput(); // 重置提示信息
            this.data.set('code', '');
            this.data.set('totpCode1', '');
            this.data.set('totpCode2', '');
        }

        onCodeInput() {
            if (this.data.get('message.text')) {
                this.data.set('message.text', '');
            }
        }

        onSendCode() {
            const authType = this.data.get('authType');
            const options = {
                url: '/api/iam/authcode/send',
                payload: {authtype: authType}
            };

            this.sendRequest(options)
                .then(data => {
                    this.data.merge('message', {
                        level: 1,
                        text: '验证码已发送'
                    });
                })
                .catch(e => {
                    this.data.set('stopCount', true);
                    this.data.merge('message', {
                        level: 0,
                        text: '验证码获取失败，请重新获取'
                    });
                });
        }
        // 根据不同类型生成请求参数
        getPayload() {
            const authType = this.data.get('authType');
            let code;
            if (authType === 'phone') {
                code = this.data.get('code');
            } else {
                const {isCreateTotp, totpCode1, totpCode2} = this.data.get('');
                code = isCreateTotp ? `${totpCode1};${totpCode2}` : totpCode1;
            }
            return {code, authtype: authType};
        }

        // 弹窗确认验证码
        onConfirmDialog() {
            // 如按钮已被锁定，则不再进行处理
            const confirmBtnLocked = this.data.get('confirmBtnLocked');
            if (confirmBtnLocked) {
                return;
            }

            // 锁定确定按钮
            this.data.set('confirmBtnLocked', true);

            const payload = this.getPayload();
            const options = {
                url: '/api/iam/authcode/verify',
                payload: payload
            };
            this.sendRequest(options)
                .then(data => {
                    this.fire('ok', {data});
                    this.data.set('open', false);

                    // 验证成功之后，延迟  3s 解锁按钮
                    setTimeout(() => {
                        this.data.set('confirmBtnLocked', false);
                    }, 3000);
                })
                .catch(e => {
                    // 验证成功之后，延迟  1s 解锁按钮
                    setTimeout(() => {
                        this.data.set('confirmBtnLocked', false);
                    }, 1000);

                    const message = {
                        level: 0,
                        text: '验证失败，请稍后重试'
                    };
                    if (e && e.code === 'BceValidationException') {
                        message.text = '验证码不正确';
                    } else if (e && e.code === 'LimitReached') {
                        message.text = '验证次数超过上限，请一小时后重试';
                    }
                    this.data.set('stopCount', true);
                    this.data.merge('message', message);
                });
        }

        onCloseDialog() {
            this.fire('cancel', '');
            this.data.set('open', false);
        }

        sendRequest({url, payload}: any) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    method: 'POST',
                    url,
                    dataType: 'json',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                        'X-Request-By': 'ERApplication',
                        csrftoken: this.data.get('csrftoken')
                    },
                    data: JSON.stringify(payload),
                    success(data) {
                        if (data.success === true || data.success === 'true') {
                            resolve(data);
                        } else {
                            reject(data);
                        }
                    },
                    error: reject
                });
            });
        }
    };
}
