/**
 * Copyright 2018 Baidu Inc. All rights reserved.
 *
 * @file api/plugins/request.ts
 * <AUTHOR>
 */
import Notification from '../components/notification/main';

import './notification.less';

export default (contextPipe: {
    getCsrfToken: Function,
    getCurrentRegionId: Function,
    getCurrentRegion: Function,
    getLogoutUrl?: Function
    currentLanguage?: string
    defaultRegion?: string
}) => (req: any, next: () => {}) => {

    const logoutUrl = contextPipe.getLogoutUrl && contextPipe.getLogoutUrl();

    req.url += req.url.indexOf('?') > 0 ? '&' : '?';

    // url中没有locale参数，增加locale参数
    if (!req.url.match(/locale=/)){
        req.url += 'locale=' + (contextPipe.currentLanguage || 'zh-cn') + '&';
    }

    req.url += '_=' + +new Date();

    let currentRegion = req.headers.region;
    if (!currentRegion) {
        currentRegion = contextPipe.getCurrentRegion();

        if (typeof currentRegion === 'object') {
            currentRegion = currentRegion.id || currentRegion;
        }
    }
    // 全局产品默认regeion设置
    const defaultRegion: string = contextPipe.defaultRegion
        || (window.PROJECT_CONTEXT && window.PROJECT_CONTEXT.DEFAULT_REGION)
        || 'bj';
    const postRegion = currentRegion === 'global' ? defaultRegion : currentRegion;

    // 默认开启CSRF token校验
    if (req.CSRFToken !== false) {
        const csrfToken = contextPipe.getCsrfToken();
        if (csrfToken) {
            req.headers['csrftoken'] = csrfToken.replace(/"/g, '');
        }

        // 开启的情况下，没有token认为登录时效了，console-hub没有从服务端跳转，从前端做一个重定向逻辑
        else if (logoutUrl) {
            const url = logoutUrl.substring(0, logoutUrl.length - 6);
            // 适配虚商鉴权失败跳转
            if (window.ISXSCONSOLE && /login.bce(test)?.baidu.com/.test(logoutUrl)) {
                Notification.error('当前登录已过期，请重新登录', {duration: -1, width: 400});
                window.parent.postMessage(
                    {
                        code: 'authentication failed'
                    },
                    window.XSREFERRER
                );
            }
            else {
                location.href = url + '?redirect=' + location.href;
            }
            return;
        }
    }

    if (currentRegion !== '') {
        req.headers['X-Region'] = postRegion;
    }

    if (sessionStorage.getItem('X-Bce-Access-Version')) {
        req.headers['X-Bce-Access-Version'] = sessionStorage.getItem('X-Bce-Access-Version');
    }

    //  添加埋点统计的记录时间
    req.startTime = req.startTime || new Date().getTime();

    next();
};
