/**
 * 请求埋点
 * @file api/plugins/track.ts
 * <AUTHOR>
 */

export default (client: any) => (res: any, next:() => {}) => {
    const {success, message} = res.data;
    const status = res.request && res.request.status;
    const xhr = res.request;
    const requestId = res.headers && res.headers['x-bce-request-id'];
    const options = res.config;
    const trackLog = window.$bceTrack.trackLog;

    const baseData = {
        'context_url': options.url.replace(/\?[^#]*/, ''),
        'request_id': requestId,
        'request_method': options.method.toUpperCase(),
        'spent': Date.now() - options.startTime,
        'actionPath': window.location.href.replace(/(^.*#|~.*)/g, ''),
        'href': window.location.href.replace(/\?[^#]*/, '')
    }

    try {
        //  正常请求都会返回200，但是也要考虑到请求不通的情况
        if (status === 200) {
            if (success === false || success === 'false') {
                let errorMessage = '';
                if (message) {
                    errorMessage = message.global ? message.global
                        : (message.noSession || '系统提示：请求失败(未知错误)')
                }
                // 状态码200，请求失败
                trackLog({
                    ...baseData,
                    'target': 'ajax-failure',
                    'field_title': options.data,
                    'value': status,
                    'content': xhr && xhr.responseText,
                    'title': errorMessage
                });
            }
            else {
                // 请求成功
                trackLog({
                    ...baseData,
                    'target': 'ajax-success',
                });
            }
        }
        else {
            // 状态码异常，请求失败
            trackLog({
                ...baseData,
                'target': 'ajax-error',
                'field_title': options.data,
                'value': status,
                'content': xhr && xhr.responseText,
                'title': '服务器错误：' + res.status
            });
        }
    } catch(e) {
        next()
    }

    next();
}
