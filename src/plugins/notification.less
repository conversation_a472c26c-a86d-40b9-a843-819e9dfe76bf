@font-primary-color: #151B26;
@font-blue-color: #2468F2;

.bce-httpclient-content {
    font-size: 14px;
    color: @font-primary-color;
    line-height: 20px;
    font-weight: 500;
    margin-right: 4px;
    word-break: break-all;
}

.bce-httpclient-request-id {
    color: @font-primary-color;
    word-break: break-all;
    min-height: 20px;
    margin-top: 6px;
    line-height: 20px;
}

.bce-httpclient-copy {
    position: relative;
    margin-left: 4px;
    cursor: pointer;
    &:before {
        content: '';
        position: absolute;
        top: 0;
        background-color: @font-blue-color;
        background-repeat: no-repeat;
        width: 14px;
        height: 14px;
        mask-image: url('../assets/copy.svg');
    }
}

#bce-httpclient-title {
    margin-bottom: 6px;
    word-break: keep-all;
    a, a:visited, a:hover, a:active {
        text-decoration: none;
        color: @font-blue-color;
    }
}
