/**
 * 异常请求报错处理
 * @file api/plugins/notifications.js
 * <AUTHOR>
 */
import {defineComponent} from 'san';
import Notification from '../components/notification/main';

import './notification.less';

/**
 * 服务器报错 原bui的asInternalError方法
 * @param {string} url
 * @param {number} httpcode
 */
const getInternalError = (url: string, httpcode: number) => {
    const content = defineComponent({
        template: String.raw`
            <template>
                <div>
                    <span class="bce-httpclient-content">服务器错误：${httpcode}</span>
                </div>
                <div class="bce-httpclient-request-id">
                    请求路径：{{url}}
                </div>
            </template>
        `,
        initData() {
            return {
                url
            }
        }
    });
    return content;
};

const getGlobalError = (
    content: string,
    requestId: string,
    ticketQuery: Record<string, string>,
    ticket: string,
    module: string | number
) => {
    const globalContent = defineComponent({
        initData() {
            const query = [`module=${module}`, `requestId=${requestId}`];
            for (const key in ticketQuery) {
                query.push(`${key}=${ticketQuery[key]}`);
            }
            const urlQuery = query.join('&');
            const ticketHref = ticket || `http://ticket.bce.baidu.com/?#/ticket/create~${urlQuery}`;
            // 判断是公有云还是私有云
            const pattern = /(bce|bcetest)\.baidu\.com/;
            const hostname = window.location.hostname;
            const isPublicCloud = pattern.test(hostname);
            let showTicketNotification;
            if (isPublicCloud) {
                // 公有云
                showTicketNotification = requestId && !window.ISXSCONSOLE;
            }
            else {
                // 私有云
                showTicketNotification = 
                    requestId
                    && !window.ISXSCONSOLE
                    && window.PROJECT_CONTEXT
                    && window.PROJECT_CONTEXT.DOMAINS
                    && window.PROJECT_CONTEXT.DOMAINS.TICKET
            }
            
            return {
                ticketHref,
                requestId,
                ISXSCONSOLE: window.ISXSCONSOLE,
                showTicketNotification
            };
        },
        template: String.raw`
            <template>
                <div id="bce-httpclient-title">
                    <span class="bce-httpclient-content">
                        ${content}
                    </span>
                    <a target="_blank"
                        s-if="showTicketNotification"
                        href="{{ticketHref}}"
                    >
                        提交工单
                    </a>
                </div>
                <div s-if="requestId" class="bce-httpclient-request-id">
                    错误码：{{requestId}}
                    <i class="bce-httpclient-copy" on-click="copyRequestId"/>
                </div>
            </template>
        `,
        copyRequestId() {
            const requestId = this.data.get('requestId');
            // create copy document
            const isRTL = document.documentElement.getAttribute('dir') === 'rtl';
            let copyDom = document.createElement('textarea');
            // Move element out of screen horizontally
            copyDom.style.position = 'absolute';
            copyDom.style[isRTL ? 'right' : 'left'] = '-9999px';
            // Move element to the same position vertically
            const yPosition = window.pageYOffset || document.documentElement.scrollTop;
            copyDom.style.top = yPosition + 'px';
            copyDom.value = requestId;
            const targetDom = document.querySelector('#bce-httpclient-title');
            targetDom.appendChild(copyDom);
            copyDom.select();
            document.execCommand('copy');
            targetDom.removeChild(copyDom);
            copyDom = null;
        }
    });

    return globalContent;
};

export default (client: any, contextPipe: any = {}) => (res:any, next:() => {}) => {
    const {success, message, ticketQuery, code} = res.data;
    const status = res.status;
    const url = res.config.url;
    const requestId = res.headers && res.headers['x-bce-request-id'];
    const options = res.config;

    // firefox中请求未完成时刷新页面，仍然执行notifications问题
    if (status === 0 && res.data === '' && res.statusText === '') {
        return;
    }

    // 重定向
    if (message && (typeof message.redirect !== 'undefined')) {
        // 适配虚商鉴权失败跳转
        if (window.ISXSCONSOLE && (code === 'AuthenticationFailed' || /login.bce(test)?.baidu.com/.test(message.redirect))) {
            Notification.error('当前登录已过期，请重新登录', {duration: -1, width: 400});
            window.parent.postMessage(
                {
                    code: 'authentication failed'
                },
                window.XSREFERRER
            );
        }
        else {
            location.href = message.redirect;
        };
        client.res.abort();
        return;
    }

    //  静默模式直接返回不处理
    if (options['x-silent'] || options['X-Silence'] || (options['x-silent-codes'] && options['x-silent-codes'].indexOf(code) !== -1)) {
        next();
        return;
    }
    //  正常请求都会返回200，但是也要考虑到请求不通的情况
    if (status === 200) {
        //  请求成功只需要处理返回success为false的
        if (success === false || success === 'false') {
            if (message && message.global) {
                // 全局错误，包含requestId
                const ticket = contextPipe && contextPipe.getDomains && contextPipe.getDomains().ticket || '';
                const isPublicConsole = ticket.indexOf('baidu.com') !== -1;
                const module = url.split('/')[2].toUpperCase();
                const globalTicket = isPublicConsole ? '' : ticket;
                const content = getGlobalError(message.global, requestId, ticketQuery, globalTicket, module);
                Notification.error(content, {duration: -1, width: 400});
            }
            else if (message && message.noSession) {
                // 系统超时
                Notification.error(message.noSession, {duration: -1});
            }
            else if (message && !message.field) {
                // 未知错误
                Notification.error('系统提示：请求失败(未知错误)', {duration: -1});
            }

            //  统一处理success false为500
            res.status = 500;
        }
    }
    else if (status === 'bce_exception') {
        const content = getInternalError(url, res.httpcode);
        Notification.error(content, {duration: -1, width: 400});
    }
    else {
        const content = getInternalError(url, status);
        Notification.error(content, {duration: -1, width: 400});
    }
    next();
};
