/**
 * Copyright 2018 Baidu Inc. All rights reserved.
 *
 * @file src/plugins/mfa.js
 * <AUTHOR>
 */
import {bizMFACheck} from '@baiducloud/bce-ui/san';
import MFACheck from '../components/mfa/MFACheck';
import './mfa.less';

const getAuthorizationToken = (csrftoken:string, data:any) => new Promise((resolve, reject) => {
    let Component;
    try {
        const sui = window.require('@baidu/sui');
        Component = MFACheck(sui);
    } catch (error) {
        Component = bizMFACheck;
    }
    const mfaComponent = new Component({
        data: {
            open: true,
            mobile: data.detail.mobile,
            totp: data.detail.totp,
            csrftoken,
            changePhone: !window.ISXSCONSOLE,
            showPhoneNumber: !data.detail.isVsAccount
        }
    });
    mfaComponent.attach(document.body);

    mfaComponent.on('ok', (e:any) => {
        mfaComponent.dispose();
        resolve(e.data);
    });
    mfaComponent.on('cancel', () => {
        mfaComponent.dispose();
        reject();
    });
});

export default (client: any) => async (res:any, next:() => {}) => {

    const {code, message, cancelled} = res.data;

    if ((code === 'MFARequired' || code === 'MFACheckedFailed') && !cancelled) {

        const config = res.config;
        const csrftoken = config.headers.csrftoken;

        //  修改res的状态为202，后续会被当成成功请求处理，但是不会作为resolve返回
        res.status = 202;

        try {
            await getAuthorizationToken(csrftoken, message);
            const stacks = client.res.stack;
            const lastPlugin = stacks[stacks.length - 1];
            client.res.abort();

            // 重发请求，相当于进行一个新的请求
            try {
                res.data = await client.request(config);
                res.status = 200;
                client.res.index = 3;
                client.res.use(lastPlugin);
            }
            catch (error) {
                res.data = error;
            }
        }
        catch (e) {
            // 增加customHandleMfaErr，若customHandleMfaErr为true，则取消验证码验证时用户自行处理异常
            (!config?.customHandleMfaErr) && client.res.abort();
            res.data.cancelled = true;
        }
    }

    next();
}
