/**
 * 为业务提供一些必要的插件
 * @file api/index.ts
 * <AUTHOR>
 */

import Client from '@baiducloud/restclient';
import requestPlugin from './plugins/request';
import notificationPlugin from './plugins/notification';
import mfaPlugin from './plugins/mfa';
import trackPlugin from './plugins/track';
import consoleProxy from './plugins/consoleProxy';

interface Client {
    [key:string]:any
}

interface ContextPipe {
    getCsrfToken: Function,
    getCurrentRegionId: Function,
    getCurrentRegion: Function,
    getLogoutUrl: Function
}

export default class extends Client {

    /**
     * 构造方法，需要传递上下文参数获取方法
     *
     * @param {Object=} config 默认配置参数
     * @param {Object=} contextPipe 上下文参数获取方法
     */
    constructor(config:object, contextPipe:ContextPipe) {
        super(Object.assign({
            validateStatus: (status:number) => status === 200
        }, config));
        const ONLINE_DOMAIN = 'console.bce.baidu.com';
        if (window.amis && location.hostname.indexOf(ONLINE_DOMAIN) === -1) {
            this.req.use(consoleProxy(this));
        }
        this.req.use(requestPlugin(contextPipe));
        this.res.use(notificationPlugin(this, contextPipe));
        this.res.use(mfaPlugin(this));

        if (window.$bceTrack && window.$bceTrack.trackLog) {
            this.res.use(trackPlugin(this));
        }

        this.initMethods();
    }

    initMethods() {
        const methods = ['get', 'put', 'post', 'delete'];
        methods.forEach(method => {
            (this as Client)[method] = (...args:any) => super[method](...args).then((data:any) => {
                if (data.hasOwnProperty('page')) {
                    return data.page;
                }
                if (data.hasOwnProperty('result')) {
                    return data.result;
                }
                return data;
            });
        });
    }
}
